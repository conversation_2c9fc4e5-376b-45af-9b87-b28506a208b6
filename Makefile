.PHONY: setup build run clean

# Setup development environment
setup:
	@command -v uv >/dev/null 2>&1 || { echo "❌ uv is not installed. Please install it first: https://docs.astral.sh/uv/"; exit 1; }
	@if [ ! -d ".venv" ]; then \
		echo "📦 Creating virtual environment..."; \
		uv venv; \
		echo "✅ Virtual environment created at .venv/"; \
	else \
		echo "✅ Virtual environment already exists"; \
	fi
	@echo "📦 Installing dependencies in virtual environment..."
	@uv sync --extra dev --extra test
	@mkdir -p logs
	@if [ ! -f .env ]; then \
		echo "# KiotViet MCP Server Configuration" > .env; \
		echo "KIOTVIET_CLIENT_ID=your_client_id_here" >> .env; \
		echo "KIOTVIET_CLIENT_SECRET=your_client_secret_here" >> .env; \
		echo "KIOTVIET_RETAILER=your_retailer_name_here" >> .env; \
		echo "LOG_LEVEL=DEBUG" >> .env; \
		echo "✅ Created .env template - please fill in your KiotViet credentials"; \
	else \
		echo "✅ .env file already exists"; \
	fi
	@echo "🎉 Setup completed! Virtual environment ready at .venv/"

# Build package
build:
	@if [ ! -d ".venv" ]; then \
		echo "❌ Virtual environment not found. Run 'make setup' first."; \
		exit 1; \
	fi
	@echo "📦 Building package..."
	@uv build
	@echo "✅ Package built successfully"

# Run server
run:
	@if [ ! -d ".venv" ]; then \
		echo "❌ Virtual environment not found. Run 'make setup' first."; \
		exit 1; \
	fi
	@echo "🚀 Starting server..."
	@.venv/bin/python -m albatross_kiotviet_mcp.main

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	@find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@find . -type f -name "*.pyc" -delete 2>/dev/null || true
	@rm -rf build/ dist/ *.egg-info/ .pytest_cache/ .coverage htmlcov/ .mypy_cache/
	@echo "✅ Cleanup completed"


