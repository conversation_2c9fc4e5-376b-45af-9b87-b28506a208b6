# KiotViet MCP Server

A professional Model Context Protocol (MCP) server for integrating with KiotViet Public API. This server enables AI assistants like <PERSON> to interact with KiotViet's retail management system through natural language queries.

## Features

- **🏗️ Clean Architecture**: Domain-driven design with clear separation of concerns
- **🔐 Secure Authentication**: Automatic OAuth2 token management with refresh handling
- **🛠️ MCP Tools**: Categories, products, and branches retrieval with pagination
- **🔒 Type Safety**: Complete type hints with Pydantic validation
- **🧪 Comprehensive Testing**: Unit and integration tests with real API validation
- **⚡ Production Ready**: Error handling, logging, and async/await throughout
- **🔧 Extensible**: Easy-to-follow patterns for adding new tools

## Project Structure

This project follows **Clean Architecture** principles with clear separation of concerns:

```
src/albatross_kiotviet_mcp/
├── domain/                        # Business logic layer
│   ├── entities/                  # Domain entities (Category, Product, Branch)
│   │   ├── category.py           # Category entity with Pydantic validation
│   └── interfaces/               # Abstract interfaces
│       └── api_client.py         # IKiotVietAPIClient interface
├── infrastructure/               # External concerns layer
│   ├── api/                      # API client implementations
│   │   ├── base_client.py        # Base HTTP client with auth
│   │   └── kiotviet_client.py    # KiotViet-specific API client
│   ├── auth/                     # Authentication management
│   │   └── token_manager.py     # OAuth2 token lifecycle
│   └── config/                   # Configuration management
│       └── settings.py           # Pydantic settings with env vars
├── tools/                        # MCP tools layer
│   ├── utils.py                  # Utility functions for validation and result processing
│   ├── categories.py             # Categories MCP tool (function-based)
│   ├── invoices.py               # Invoices MCP tool (function-based)
│   └── revenue.py                # Revenue calculation MCP tool (function-based)
├── application/                  # Application services layer
│   └── services/                 # Business services (future)
├── main.py                       # Application entry point
└── server.py                     # FastMCP server setup and tool registration
```

### Architecture Benefits

- **Domain Layer**: Pure business logic, no external dependencies
- **Infrastructure Layer**: Handles external APIs, configuration, authentication
- **Tools Layer**: MCP-specific implementations with clean interfaces
- **Application Layer**: Orchestrates business workflows (extensible)

## Getting Started

### Prerequisites

- **Python 3.10+**
- **KiotViet API Credentials**: Client ID, Client Secret, Retailer name
- **uv** (recommended) or pip for dependency management

### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd albatross-kiotviet-mcp

# Install dependencies with uv (recommended)
uv sync

# Or with pip
pip install -e .
```

### 2. Configuration

Create a `.env` file with your KiotViet credentials:

```env
KIOTVIET_CLIENT_ID=your_client_id_here
KIOTVIET_CLIENT_SECRET=your_client_secret_here
KIOTVIET_RETAILER=your_retailer_name_here
```

### 3. Run the MCP Server

```bash
# Using uv (recommended)
uv run albatross-kiotviet-mcp-server

# Or using the installed script
albatross-kiotviet-mcp-server

# Development mode with auto-reload
uv run python -m albatross_kiotviet_mcp.main
```

### 4. Connect from Claude Desktop

Edit your Claude Desktop configuration file:

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "kiotviet": {
      "command": "uv",
      "args": [
        "--directory",
        "/path/to/albatross-kiotviet-mcp",
        "run",
        "albatross-kiotviet-mcp-server"
      ],
      "env": {
        "PYTHONUNBUFFERED": "1"
      }
    }
  }
}
```

Restart Claude Desktop to load the configuration.

## Available Tools
- get_categories (Retrieve product categories with pagination and hierarchical support).
- get_invoices_by_day (Retrieve invoices for a specific date range with pagination support).
- calculate_daily_revenue (Calculate revenue metrics for a specific date range from KiotViet invoices).