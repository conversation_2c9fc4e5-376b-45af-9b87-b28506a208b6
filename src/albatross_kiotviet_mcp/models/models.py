"""Data models for KiotViet API responses."""

from typing import List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field


class Category(BaseModel):
    """Individual category data model."""
    id: int = Field(alias="categoryId")  # KiotViet API uses 'categoryId' instead of 'id'
    categoryName: str
    categoryCode: Optional[str] = None
    parentId: Optional[int] = None
    hasChild: bool = False
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None
    rank: Optional[int] = None  # Add rank field that appears in API response


class CategoryResponse(BaseModel):
    """Response model for categories API endpoint."""
    total: int
    pageSize: int
    data: List[Category]


class Invoice(BaseModel):
    """Individual invoice data model."""
    id: int
    code: str
    total: Optional[float] = None
    createdDate: Optional[datetime] = None
    customerName: Optional[str] = None
    customerCode: Optional[str] = None
    branchId: Optional[int] = None
    branchName: Optional[str] = None
    soldById: Optional[int] = None
    soldByName: Optional[str] = None
    status: Optional[int] = None
    discount: Optional[float] = None
    description: Optional[str] = None


class InvoiceResponse(BaseModel):
    """Response model for invoices API endpoint."""
    total: int
    pageSize: int
    data: List[Invoice]
