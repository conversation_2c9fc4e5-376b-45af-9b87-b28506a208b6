"""Revenue tool for KiotViet MCP operations."""

from typing import Dict, Any, List, Union
from datetime import datetime
from ..api.api_client import KiotVietAPIClient
from ..models.models import Invoice
from .get_invoices_by_day_tool import parse_and_validate_dates


async def calculate_daily_revenue(
    api_client: KiotVietAPIClient,
    from_date: Union[str, datetime],
    to_date: Union[str, datetime],
    include_details: bool = False
) -> Dict[str, Any]:
    """Calculate revenue metrics for a specific date range from KiotViet invoices.
    
    This function calculates comprehensive revenue metrics by fetching all invoices in the
    specified date range and computing totals, averages, and optionally detailed breakdowns.
    
    Args:
        api_client: KiotViet API client instance
        from_date: Start date (datetime object or ISO string like '2025-01-15')
        to_date: End date (datetime object or ISO string like '2025-01-15')
        include_details: Whether to include detailed invoice breakdown (default: False)
    
    Returns:
        Dictionary containing revenue metrics and summary data
        
    Raises:
        ValueError: If parameters are invalid
    """
    # Parse and validate dates
    parsed_from_date, parsed_to_date = parse_and_validate_dates(from_date, to_date)
    
    # Fetch all invoices for the date range
    all_invoices = []
    current_item = 0
    page_size = 100  # Use max page size for efficiency
    
    while True:
        result = await api_client.get_invoices_by_day(
            from_date=parsed_from_date,
            to_date=parsed_to_date,
            page_size=page_size,
            current_item=current_item,
            order_direction="Asc"
        )
        
        if not result.data:
            break
            
        all_invoices.extend(result.data)
        
        # Check if we have more data
        if len(result.data) < page_size:
            break
            
        current_item += page_size
    
    # Calculate revenue metrics
    return calculate_revenue_metrics(all_invoices, parsed_from_date, parsed_to_date, include_details)


def calculate_revenue_metrics(
    invoices: List[Invoice], 
    from_date: datetime, 
    to_date: datetime, 
    include_details: bool
) -> Dict[str, Any]:
    """Calculate revenue metrics from invoice data.
    
    Args:
        invoices: List of invoice data
        from_date: Start date for the calculation
        to_date: End date for the calculation
        include_details: Whether to include detailed breakdown
        
    Returns:
        Dictionary with revenue metrics
    """
    if not invoices:
        return {
            "summary": {
                "total_revenue": 0,
                "total_invoices": 0,
                "average_invoice_value": 0,
                "date_range": {
                    "from": from_date.isoformat(),
                    "to": to_date.isoformat()
                }
            },
            "details": [] if include_details else None
        }
    
    # Calculate totals
    total_revenue = sum(float(invoice.total or 0) for invoice in invoices)
    total_invoices = len(invoices)
    average_invoice_value = total_revenue / total_invoices if total_invoices > 0 else 0
    
    # Build summary
    summary = {
        "total_revenue": round(total_revenue, 2),
        "total_invoices": total_invoices,
        "average_invoice_value": round(average_invoice_value, 2),
        "date_range": {
            "from": from_date.isoformat(),
            "to": to_date.isoformat()
        }
    }
    
    # Build result
    result = {"summary": summary}
    
    if include_details:
        result["details"] = [
            {
                "id": invoice.id,
                "code": invoice.code,
                "total": float(invoice.total or 0),
                "created_date": invoice.createdDate,
                "customer": invoice.customerName
            }
            for invoice in invoices
        ]
    
    return result
