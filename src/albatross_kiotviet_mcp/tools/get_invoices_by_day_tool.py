"""Invoices tool for KiotViet MCP operations."""

from typing import Dict, Any, Union
from datetime import datetime
from ..api.api_client import KiotVietAPIClient
from ..models.models import InvoiceResponse


def parse_and_validate_dates(from_date: Union[str, datetime], to_date: Union[str, datetime]) -> tuple[datetime, datetime]:
    """Parse and validate date inputs.
    
    Args:
        from_date: Start date as string or datetime
        to_date: End date as string or datetime
        
    Returns:
        Tuple of parsed datetime objects
        
    Raises:
        ValueError: If dates are invalid or from_date > to_date
    """
    def parse_date(date_input: Union[str, datetime]) -> datetime:
        if isinstance(date_input, datetime):
            return date_input
        elif isinstance(date_input, str):
            try:
                # Try ISO format first
                return datetime.fromisoformat(date_input.replace('Z', '+00:00'))
            except ValueError:
                try:
                    # Try YYYY-MM-DD format
                    return datetime.strptime(date_input, '%Y-%m-%d')
                except ValueError:
                    raise ValueError(f'Invalid date format: {date_input}. Use YYYY-MM-DD or ISO format')
        else:
            raise ValueError(f'Date must be string or datetime, got {type(date_input)}')
    
    parsed_from = parse_date(from_date)
    parsed_to = parse_date(to_date)
    
    if parsed_from > parsed_to:
        raise ValueError('from_date must be before or equal to to_date')
    
    return parsed_from, parsed_to


async def get_invoices_by_day(
    api_client: KiotVietAPIClient,
    from_date: Union[str, datetime],
    to_date: Union[str, datetime],
    page_size: int = 50,
    current_item: int = 0,
    order_direction: str = "Asc"
) -> Dict[str, Any]:
    """Get invoices for a specific date range from KiotViet API.
    
    Args:
        api_client: KiotViet API client instance
        from_date: Start date (datetime object or ISO string)
        to_date: End date (datetime object or ISO string)
        page_size: Number of items per page (default: 50, max: 100)
        current_item: Starting item index for pagination (default: 0)
        order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
    
    Returns:
        Dictionary containing invoice data from the API
        
    Raises:
        ValueError: If parameters are invalid
    """
    # Parse and validate dates
    parsed_from_date, parsed_to_date = parse_and_validate_dates(from_date, to_date)
    
    # Validate pagination
    if not (1 <= page_size <= 100):
        raise ValueError("page_size must be between 1 and 100")
    if current_item < 0:
        raise ValueError("current_item must be non-negative")
    if order_direction not in ['Asc', 'Desc']:
        raise ValueError("order_direction must be 'Asc' or 'Desc'")
    
    # Make API call
    result = await api_client.get_invoices_by_day(
        from_date=parsed_from_date,
        to_date=parsed_to_date,
        page_size=page_size,
        current_item=current_item,
        order_direction=order_direction
    )
    
    # Convert to dictionary for consistent output
    return result.model_dump()
