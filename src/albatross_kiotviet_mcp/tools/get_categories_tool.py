"""Categories tool for KiotViet MCP operations."""

from typing import Dict, Any
from ..api.api_client import KiotVietAPIClient
from ..models.models import CategoryResponse


def validate_pagination_params(page_size: int, current_item: int) -> None:
    """Validate pagination parameters.
    
    Args:
        page_size: Number of items per page
        current_item: Starting item index
        
    Raises:
        ValueError: If parameters are invalid
    """
    if not (1 <= page_size <= 100):
        raise ValueError("page_size must be between 1 and 100")
    if current_item < 0:
        raise ValueError("current_item must be non-negative")


def validate_order_direction(order_direction: str) -> None:
    """Validate order direction parameter.
    
    Args:
        order_direction: Sort order direction
        
    Raises:
        ValueError: If order direction is invalid
    """
    if order_direction not in ['Asc', 'Desc']:
        raise ValueError("order_direction must be 'Asc' or 'Desc'")


async def get_categories(
    api_client: KiotVietAPIClient,
    page_size: int = 50,
    current_item: int = 0,
    order_direction: str = "Asc",
    hierarchical_data: bool = False
) -> Dict[str, Any]:
    """Get product categories from KiotViet API with pagination support.
    
    This function retrieves product categories from the KiotViet API with support for pagination,
    sorting, and hierarchical data structure options.
    
    Args:
        api_client: KiotViet API client instance
        page_size: Number of items per page (default: 50, max: 100)
        current_item: Starting item index for pagination (default: 0)
        order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
        hierarchical_data: Whether to return hierarchical structure (default: False)
    
    Returns:
        Dictionary containing category data from the API
        
    Raises:
        ValueError: If parameters are invalid
    """
    # Validate parameters
    validate_pagination_params(page_size, current_item)
    validate_order_direction(order_direction)
    
    # Make API call
    result = await api_client.get_categories(
        page_size=page_size,
        current_item=current_item,
        order_direction=order_direction,
        hierarchical_data=hierarchical_data
    )
    
    # Convert to dictionary for consistent output
    return result.model_dump()
