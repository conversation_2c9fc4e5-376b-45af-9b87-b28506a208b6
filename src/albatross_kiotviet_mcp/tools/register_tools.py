"""MCP tool registration that delegates to tool implementations."""

from typing import Annotated, Dict, Any, Union
from datetime import datetime
from fastmcp import FastMCP
from pydantic import Field

from ..config import get_api_client
from . import calculate_daily_revenue_tool, get_categories_tool, get_invoices_by_day_tool


def register_categories_tool(mcp: FastMCP) -> None:
    """Register the categories tool with the FastMCP server."""
    
    @mcp.tool()
    async def get_categories(
        page_size: Annotated[int, Field(description="Number of items per page (1-100)", ge=1, le=100)] = 50,
        current_item: Annotated[int, Field(description="Starting item index for pagination", ge=0)] = 0,
        order_direction: Annotated[str, Field(description="Sort order: Asc or Desc")] = "Asc",
        hierarchical_data: Annotated[bool, Field(description="Return hierarchical structure")] = False
    ) -> Dict[str, Any]:
        """Get product categories from KiotViet API with pagination support.
        
        This tool retrieves product categories from the KiotViet API with support for pagination,
        sorting, and hierarchical data structure options.
        
        Args:
            page_size: Number of items per page (default: 50, max: 100)
            current_item: Starting item index for pagination (default: 0)
            order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
            hierarchical_data: Whether to return hierarchical structure (default: False)
        
        Returns:
            Dictionary containing category data from the API
        """
        api_client = get_api_client()
        async with api_client:
            return await get_categories_tool.get_categories(
                api_client=api_client,
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                hierarchical_data=hierarchical_data
            )


def register_invoices_tool(mcp: FastMCP) -> None:
    """Register the invoices tool with the FastMCP server."""
    
    @mcp.tool()
    async def get_invoices_by_day(
        from_date: Annotated[Union[str, datetime], Field(description="Start date (YYYY-MM-DD or ISO format)")],
        to_date: Annotated[Union[str, datetime], Field(description="End date (YYYY-MM-DD or ISO format)")],
        page_size: Annotated[int, Field(description="Number of items per page (1-100)", ge=1, le=100)] = 50,
        current_item: Annotated[int, Field(description="Starting item index for pagination", ge=0)] = 0,
        order_direction: Annotated[str, Field(description="Sort order: Asc or Desc")] = "Asc"
    ) -> Dict[str, Any]:
        """Get invoices for a specific date range from KiotViet API.
        
        This tool retrieves invoices from the KiotViet API for a specified date range
        with support for pagination and sorting.
        
        Args:
            from_date: Start date (datetime object or ISO string like '2025-01-15')
            to_date: End date (datetime object or ISO string like '2025-01-15')
            page_size: Number of items per page (default: 50, max: 100)
            current_item: Starting item index for pagination (default: 0)
            order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
        
        Returns:
            Dictionary containing invoice data from the API
        """
        api_client = get_api_client()
        async with api_client:
            return await get_invoices_by_day_tool.get_invoices_by_day(
                api_client=api_client,
                from_date=from_date,
                to_date=to_date,
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction
            )


def register_revenue_tool(mcp: FastMCP) -> None:
    """Register the revenue tool with the FastMCP server."""
    
    @mcp.tool()
    async def calculate_daily_revenue(
        from_date: Annotated[Union[str, datetime], Field(description="Start date (YYYY-MM-DD or ISO format)")],
        to_date: Annotated[Union[str, datetime], Field(description="End date (YYYY-MM-DD or ISO format)")],
        include_details: Annotated[bool, Field(description="Include detailed invoice breakdown")] = False
    ) -> Dict[str, Any]:
        """Calculate revenue metrics for a specific date range from KiotViet invoices.
        
        This tool calculates comprehensive revenue metrics by fetching all invoices in the
        specified date range and computing totals, averages, and optionally detailed breakdowns.
        
        Args:
            from_date: Start date (datetime object or ISO string like '2025-01-15')
            to_date: End date (datetime object or ISO string like '2025-01-15')
            include_details: Whether to include detailed invoice breakdown (default: False)
        
        Returns:
            Dictionary containing revenue metrics and summary data
        """
        api_client = get_api_client()
        async with api_client:
            return await calculate_daily_revenue_tool.calculate_daily_revenue(
                api_client=api_client,
                from_date=from_date,
                to_date=to_date,
                include_details=include_details
            )


def register_all_tools(mcp: FastMCP) -> None:
    """Register all MCP tools with the FastMCP server.

    Args:
        mcp: FastMCP server instance to register tools with
    """
    register_categories_tool(mcp)
    register_invoices_tool(mcp)
    register_revenue_tool(mcp)
