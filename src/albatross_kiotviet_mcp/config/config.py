"""Simplified configuration management for KiotViet MCP Server."""

from typing import Optional
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Simplified configuration for KiotViet API integration."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )
    
    # KiotViet API credentials
    kiotviet_client_id: str = Field(..., description="KiotViet Client ID")
    kiotviet_client_secret: str = Field(..., description="KiotViet Client Secret")
    kiotviet_retailer: str = Field(..., description="KiotViet Retailer Name")
    
    # API endpoints
    kiotviet_auth_url: str = Field(
        default="https://id.kiotviet.vn/connect/token",
        description="KiotViet OAuth2 token endpoint"
    )
    kiotviet_api_base_url: str = Field(
        default="https://public.kiotapi.com",
        description="KiotViet API base URL"
    )
    
    # Token settings
    kiotviet_token_buffer_seconds: int = Field(
        default=300,  # Refresh token 5 minutes before expiry
        description="Token refresh buffer in seconds"
    )
    
    # Request settings
    kiotviet_request_timeout: int = Field(
        default=30,
        description="HTTP request timeout in seconds"
    )
    kiotviet_max_retries: int = Field(
        default=3,
        description="Maximum number of retry attempts"
    )
    kiotviet_retry_delay: float = Field(
        default=1.0,
        description="Base delay between retry attempts in seconds"
    )
    
    # Logging settings
    log_level: str = Field(default="INFO", description="Logging level")
    log_file: Optional[str] = Field(default=None, description="Log file path")
    
    # Server settings
    server_name: str = Field(default="KiotVietMCPServer", description="MCP server name")
