"""KiotViet API client implementation."""

import asyncio
from typing import Optional, Union, Dict, Any
from datetime import datetime
import httpx
from loguru import logger
from ..config.config import Settings as KiotVietConfig
from ..models.models import CategoryResponse, InvoiceResponse
from .auth import TokenManager


class KiotVietAPIClient:
    """Client for interacting with KiotViet Public API.

    This class provides all functionality for KiotViet API integration including
    authentication, retry logic, error handling, and specific API methods.
    """

    def __init__(self, config: KiotVietConfig):
        """Initialize the KiotViet API client.

        Args:
            config: KiotViet configuration
        """
        self.config = config
        self.token_manager = TokenManager(config)
        self._client: Optional[httpx.AsyncClient] = None

    async def __aenter__(self) -> "KiotVietAPIClient":
        """Initialize HTTP client and authenticate."""
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.config.kiotviet_request_timeout),
            limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
        )
        return self

    async def __aexit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """Clean up HTTP client."""
        if self._client:
            await self._client.aclose()
            self._client = None

    async def _get_headers(self) -> Dict[str, str]:
        """Get headers with authentication token."""
        access_token = await self.token_manager.get_access_token()
        return {
            "Retailer": self.config.kiotviet_retailer,
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

    async def make_request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make authenticated request to KiotViet API with retry logic.

        This method handles:
        - Authentication with automatic token refresh
        - Retry logic with exponential backoff
        - Error handling and logging
        - Request/response formatting

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint path or full URL
            data: Request body data (for POST/PUT requests)

        Returns:
            Parsed JSON response from the API

        Raises:
            RuntimeError: If client is not initialized
            httpx.HTTPStatusError: For HTTP error responses
            httpx.RequestError: For network/connection errors
        """
        if not self._client:
            raise RuntimeError("Client not initialized. Use async context manager.")

        headers = await self._get_headers()
        url = endpoint if endpoint.startswith('http') else f"{self.config.kiotviet_api_base_url}{endpoint}"

        for attempt in range(self.config.kiotviet_max_retries):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1})")

                response = await self._client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data if method in ['POST', 'PUT'] else None,
                    params=data if method == 'GET' else None
                )

                response.raise_for_status()

                try:
                    result = response.json()
                    logger.debug(f"Successful {method} request to {url}")
                    return result
                except ValueError as e:
                    logger.error(f"Invalid JSON response from {url}: {e}")
                    raise

            except httpx.HTTPStatusError as e:
                logger.warning(f"HTTP error for {method} {url}: {e.response.status_code} {e.response.text}")

                # Handle authentication errors by refreshing token
                if e.response.status_code == 401:
                    logger.info("Authentication failed, refreshing token...")
                    # Token manager will handle refresh on next call
                    headers = await self._get_headers()
                    continue

                # Don't retry client errors (4xx) except 401
                if 400 <= e.response.status_code < 500 and e.response.status_code != 401:
                    raise

                # Retry server errors (5xx) and other retryable errors
                if attempt == self.config.kiotviet_max_retries - 1:
                    raise

                # Exponential backoff
                wait_time = (2 ** attempt) * self.config.kiotviet_retry_delay
                logger.info(f"Retrying in {wait_time}s...")
                await asyncio.sleep(wait_time)

            except httpx.RequestError as e:
                logger.warning(f"Request error for {method} {url}: {e}")

                if attempt == self.config.kiotviet_max_retries - 1:
                    raise

                # Exponential backoff
                wait_time = (2 ** attempt) * self.config.kiotviet_retry_delay
                logger.info(f"Retrying in {wait_time}s...")
                await asyncio.sleep(wait_time)

    async def get_categories(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> CategoryResponse:
        """Get product categories from KiotViet API.

        Args:
            page_size: Number of items per page (max 100)
            current_item: Starting item index for pagination
            order_direction: Sort order ("Asc" or "Desc")
            hierarchical_data: Whether to return hierarchical structure

        Returns:
            Validated CategoryResponse with typed data
        """
        data = {
            "pageSize": min(page_size, 100),  # Ensure max limit
            "currentItem": current_item,
            "orderDirection": order_direction,
            "hierarchicalData": hierarchical_data
        }

        raw_response = await self.make_request("GET", "/categories", data=data)
        return CategoryResponse.model_validate(raw_response)

    async def get_invoices_by_day(
        self,
        from_date: Union[str, datetime],
        to_date: Union[str, datetime],
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc"
    ) -> InvoiceResponse:
        """Get invoices for a specific date range from KiotViet API.

        Args:
            from_date: Start date (datetime object or ISO string)
            to_date: End date (datetime object or ISO string)
            page_size: Number of items per page (max 100)
            current_item: Starting item index for pagination
            order_direction: Sort order ("Asc" or "Desc")

        Returns:
            Validated InvoiceResponse with typed invoice data

        Raises:
            ValueError: If date format is invalid
            httpx.HTTPStatusError: For HTTP error responses
            httpx.RequestError: For network/connection errors
        """

        data = {
            "pageSize": min(page_size, 100),
            "currentItem": current_item,
            "orderDirection": order_direction,
            "fromPurchaseDate": from_date,
            "toPurchaseDate": to_date
        }

        raw_response = await self.make_request("GET", "/invoices", data=data)
        return InvoiceResponse.model_validate(raw_response)
