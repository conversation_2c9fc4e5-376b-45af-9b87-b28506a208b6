"""KiotViet MCP Server - Clean separation with individual tool files."""

from loguru import logger

from .config import get_settings
from .tools import register_all_tools

# Get settings and create server
settings = get_settings()

# Import FastMCP here to avoid circular imports
from fastmcp import FastMCP
mcp = FastMCP(settings.server_name)

# Register all tools from separate files
register_all_tools(mcp)


def main() -> None:
    """Entry point for running the FastMCP server."""
    logger.info(f"Starting {settings.server_name}...")

    # Validate configuration on startup
    try:
        logger.info(f"Configuration loaded successfully for retailer: {settings.kiotviet_retailer}")
        logger.info(f"API Base URL: {settings.kiotviet_api_base_url}")
        logger.info(f"Request timeout: {settings.kiotviet_request_timeout}s")
    except Exception as e:
        logger.error(f"Configuration error: {e}")
        raise

    logger.info("All tools registered successfully from separate files")
    return mcp.run()


# Backward compatibility
def run() -> None:
    """Backward compatibility function."""
    return main()